<template>
  <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
    <!-- Present Count -->
    <div
      class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center hover:shadow-md transition-shadow"
    >
      <div class="text-3xl font-bold text-green-300 mb-2">
        {{ presentCount }}
      </div>
      <div class="text-sm font-medium text-gray-600">Có mặt</div>
    </div>

    <!-- Absent Count -->
    <div
      class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center hover:shadow-md transition-shadow"
    >
      <div class="text-3xl font-bold text-red-400 mb-2">{{ absentCount }}</div>
      <div class="text-sm font-medium text-gray-600">Vắng mặt</div>
    </div>

    <!-- Late Count -->
    <div
      class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center hover:shadow-md transition-shadow"
    >
      <div class="text-3xl font-bold text-orange-400 mb-2">{{ lateCount }}</div>
      <div class="text-sm font-medium text-gray-600">Đi muộn</div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Props
interface Props {
  presentCount?: number;
  absentCount?: number;
  lateCount?: number;
}

const props = withDefaults(defineProps<Props>(), {
  presentCount: 0,
  absentCount: 0,
  lateCount: 0,
});
</script>
