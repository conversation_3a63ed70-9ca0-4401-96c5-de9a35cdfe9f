<template>
  <div
    class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
  >
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">
          Danh sách nhân viên hôm nay
        </h3>
        <div class="text-sm text-gray-500">
          {{ employees.length }} nhân viên
        </div>
      </div>
    </div>
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Nhân viên
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Check In
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Check Out
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Giờ làm
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Trạng thái
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr
            v-for="(employee, index) in employees"
            :key="index"
            class="hover:bg-gray-50 transition-colors"
          >
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div
                  class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3"
                >
                  <span class="text-sm font-medium text-white">{{
                    employee.initial
                  }}</span>
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-900">
                    {{ employee.name }}
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900">{{ employee.checkIn }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900">{{ employee.checkOut }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900">{{ employee.workHours }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span
                :class="getStatusClass(employee.status)"
                class="inline-flex px-3 py-1 text-xs font-medium rounded-full"
              >
                {{ employee.status }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
// Types
interface Employee {
  initial: string;
  name: string;
  checkIn: string;
  checkOut: string;
  workHours: string;
  status: string;
}

// Props
interface Props {
  employees?: Employee[];
}

const props = withDefaults(defineProps<Props>(), {
  employees: () => [],
});

// Functions
const getStatusClass = (status: string) => {
  switch (status) {
    case "Đã về":
      return "bg-blue-100 text-blue-800";
    case "Đang làm việc":
      return "bg-green-100 text-green-800";
    case "Vắng mặt":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};
</script>
