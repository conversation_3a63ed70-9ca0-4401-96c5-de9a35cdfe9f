<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900">Lịch Làm <PERSON></h3>
        <div class="flex items-center gap-4">
          <button @click="previousWeek" class="p-1 hover:bg-gray-100 rounded">
            <svg class="w-5 h-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <span class="text-sm font-medium text-gray-600">{{ weekRange }}</span>
          <button @click="nextWeek" class="p-1 hover:bg-gray-100 rounded">
            <svg class="w-5 h-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>
    </div>
    
    <div class="p-6">
      <!-- Week Days Header -->
      <div class="grid grid-cols-8 gap-2 mb-4">
        <div class="text-xs font-medium text-gray-500 text-center">Giờ</div>
        <div v-for="day in weekDays" :key="day.key" class="text-center">
          <div class="text-xs font-medium text-gray-500">{{ day.label }}</div>
          <div class="text-sm text-gray-900">{{ day.date }}</div>
        </div>
      </div>
      
      <!-- Time Slots -->
      <div class="space-y-2">
        <div v-for="hour in timeSlots" :key="hour" class="grid grid-cols-8 gap-2 items-center">
          <div class="text-sm text-gray-600 text-center">{{ hour }}:00</div>
          <div 
            v-for="day in weekDays" 
            :key="`${day.key}-${hour}`" 
            class="h-8 bg-gray-50 rounded border border-gray-200 relative"
          >
            <!-- Schedule indicators -->
            <div 
              v-if="hasSchedule(day.key, hour)" 
              class="absolute inset-0 bg-blue-100 rounded border border-blue-300"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { format } from "date-fns";
import { vi } from "date-fns/locale";

// Reactive state
const currentWeek = ref(new Date());

// Computed properties
const weekRange = computed(() => {
  const start = format(currentWeek.value, "dd/MM/yyyy", { locale: vi });
  const end = new Date(currentWeek.value);
  end.setDate(end.getDate() + 6);
  return `${start} - ${format(end, "dd/MM/yyyy", { locale: vi })}`;
});

const weekDays = computed(() => {
  const days = [];
  const start = new Date(currentWeek.value);
  for (let i = 0; i < 7; i++) {
    const day = new Date(start);
    day.setDate(start.getDate() + i);
    days.push({
      key: i,
      label: format(day, "EEE", { locale: vi }),
      date: format(day, "dd/MM", { locale: vi })
    });
  }
  return days;
});

const timeSlots = computed(() => [8, 9, 10, 11, 12, 13, 14, 15, 16, 17]);

// Functions
const hasSchedule = (dayKey: number, hour: number) => {
  // Mock schedule data - replace with real data
  return hour >= 8 && hour <= 17 && dayKey < 5; // Working hours Mon-Fri
};

const previousWeek = () => {
  const newWeek = new Date(currentWeek.value);
  newWeek.setDate(newWeek.getDate() - 7);
  currentWeek.value = newWeek;
};

const nextWeek = () => {
  const newWeek = new Date(currentWeek.value);
  newWeek.setDate(newWeek.getDate() + 7);
  currentWeek.value = newWeek;
};
</script>
