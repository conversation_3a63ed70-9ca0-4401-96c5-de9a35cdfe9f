<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900">Lịch sử chấm công</h3>
    </div>
    
    <div ref="scrollContainer" class="max-h-96 overflow-y-auto">
      <div class="p-6">
        <CardTimeKeeping
          v-if="!isManager"
          :dataCheckin="dataCheckin"
        />
        <CardTimeKeepingManager
          v-else
          :dataCheckin="dataCheckin"
        />

        <!-- Loading more indicator -->
        <div
          v-if="loadingMore || (hasMore && dataCheckin.length > 0)"
          class="flex justify-center py-4"
        >
          <div class="flex items-center gap-2 text-gray-500">
            <svg class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-sm">{{ loadingMore ? "Đang tải thêm..." : "Cuộn để tải thêm" }}</span>
          </div>
        </div>

        <!-- End of list indicator -->
        <div v-if="!hasMore && dataCheckin.length > 0" class="text-center py-4">
          <p class="text-sm text-gray-500">Đã hiển thị tất cả dữ liệu</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

// Props
interface Props {
  dataCheckin?: any[];
  isManager?: boolean;
  loadingMore?: boolean;
  hasMore?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  dataCheckin: () => [],
  isManager: false,
  loadingMore: false,
  hasMore: true
});

// Template refs
const scrollContainer = ref(null);

// Expose scroll container for infinite scroll
defineExpose({
  scrollContainer
});
</script>
