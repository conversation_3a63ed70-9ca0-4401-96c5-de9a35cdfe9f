import { defineStore } from "pinia";
import { ref } from "vue";

export const useStoreStore = defineStore("store", () => {
  const { getStore, getStoreChannelIdsByEmployeeId, getDetailStoreV2 } =
    useStore();
  const dataStore = ref<any>([]);
  const store = ref([]);
  const getDataStore = async (employeeId: string) => {
    try {
      const response = await getStoreChannelIdsByEmployeeId(employeeId);
      store.value = response;
    } catch (error) {
      throw error;
    }
  };

  const getData = async (employeeId: string) => {
    dataStore.value = [];
    try {
      const response = await getStoreChannelIdsByEmployeeId(employeeId);
      store.value = response;
      const results = await Promise.allSettled(
        response.map((item: any) => getDetailStoreV2(item))
      );
      dataStore.value = results
        .filter((res) => res.status === "fulfilled" && res.value.type === "pos")
        .map((res: any) => res.value);
    } catch (error) {
      console.error("Lỗi khi lấy dữ liệu cửa hàng:", error);
    }
  };
  const getDataDetailStore = async (employeeId: string) => {
    dataStore.value = [];
    try {
      const response = await getStoreChannelIdsByEmployeeId(employeeId);
      const storeDetails = await Promise.allSettled(
        response.map((item: any) => getDetailStoreV2(item))
      );
      dataStore.value = storeDetails
        .filter((res) => res.status === "fulfilled" && res.value.type === "pos")
        .map((res: any) => res.value);
    } catch (error) {
      console.error("Lỗi khi lấy danh sách cửa hàng:", error);
    }
  };
  return {
    getDataStore,
    dataStore,
    getData,
    store,
    getDataDetailStore,
  };
});
