{"name": "dms", "private": true, "version": "3.4.12", "scripts": {"dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "build:development": "env-cmd -f .env.development nuxt build", "build:production": "env-cmd -f .env.production nuxt build", "start:development": "env-cmd -f .env.development nuxt preview", "start:production": "env-cmd -f .env.production nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@heroicons/vue": "^2.1.3", "@longvansoftware/storefront-js-client": "2.9.7", "@nuxt/image": "^1.7.0", "@nuxtjs/tailwindcss": "^6.12.0", "@pinia/nuxt": "^0.5.1", "@vite-pwa/nuxt": "^0.10.3", "@vueuse/nuxt": "^10.9.0", "chart.js": "^4.4.3", "chartjs-plugin-datalabels": "^2.2.0", "chartjs-plugin-doughnutlabel-rebourne": "^3.0.0-beta.4", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "env-cmd": "^10.1.0", "flatpickr": "^4.6.13", "matrix-js-sdk": "^34.4.0", "nuxt": "^3.11.2", "nuxt-graphql-client": "^0.2.46", "nuxt-icon": "^0.6.10", "nuxt-swiper": "^1.2.2", "print-js": "^1.6.0", "tippy.js": "^6.3.7", "vue": "^3.4.27", "vue-chartjs": "^5.3.1", "vue-currency-input": "^3.2.1", "vue-flatpickr-component": "^11.0.5", "vue3-toastify": "^0.2.1", "xlsx": "^0.18.5"}, "devDependencies": {"@pinia-plugin-persistedstate/nuxt": "^1.2.0"}, "overrides": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "resolutions": {"@jridgewell/sourcemap-codec": "^1.4.15"}}