export const formatCurrency = (value: number) => {
  if (value === 0) return "0 ₫";
  const formatter = new Intl.NumberFormat("vi-VN", {
    style: "currency",
    currency: "VND",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  });
  return formatter.format(value);
};

export const formatCurrencyV2 = (value: number) => {
  if (value === 0) return "0";
  const formatter = new Intl.NumberFormat("vi-VN", {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  });
  return formatter.format(value);
};

// New function for input formatting (without currency symbol)
export const formatCurrencyInput = (value: number) => {
  if (value === 0) return "";
  return new Intl.NumberFormat("vi-VN").format(value);
};
