<template>
  <div
    v-if="isVisible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 text-sm"
    @click.self="cancel"
  >
    <div
      class="bg-white rounded-lg shadow-lg p-4 max-w-sm w-full animate-popup"
    >
      <div class="flex items-center justify-between pb-2">
        <div></div>
        <div class="font-bold">Hoàn thành đơn hàng</div>
        <div @click="cancel" class="text-red-600 cursor-pointer">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4 md:size-5"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg>
        </div>
      </div>
      <!-- Phần nội dung -->
      <div class="max-h-[350px] overflow-y-auto">
        Danh sách đơn hàng đang cần hoàn thành
        <div
          v-for="order in listOrderComplete"
          class="max-h-[60px] overflow-y-scroll"
        >
          <span class="font-semibold">{{ order?.id }}</span>
        </div>
      </div>
      <div class="flex items-center justify-end mt-2">
        <button
          @click="handleConfirm"
          class="bg-primary text-white px-2 py-1 rounded"
        >
          Xác nhận
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
const emit = defineEmits(["confirm", "cancel"]);
const props = defineProps(["listOrderComplete"]);
const isVisible = ref(true);
const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};
const handleConfirm = () => {
  emit("confirm");
  isVisible.value = false;
};
</script>
