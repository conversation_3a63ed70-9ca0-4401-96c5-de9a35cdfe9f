// TimeKeeping related types

export interface Employee {
  initial: string;
  name: string;
  checkIn: string;
  checkOut: string;
  workHours: string;
  status: 'Đã về' | 'Đang làm việc' | 'Vắng mặt';
}

export interface TimeKeepingStats {
  presentCount: number;
  absentCount: number;
  lateCount: number;
}

export interface WeekDay {
  key: number;
  label: string;
  date: string;
}

export interface TimeKeepingData {
  id: string;
  createdStamp: number;
  workEffortTypeId: string;
  owner?: {
    name: string;
  };
}

export interface UserProfile {
  name: string;
  roles: string[];
  id: string;
}
