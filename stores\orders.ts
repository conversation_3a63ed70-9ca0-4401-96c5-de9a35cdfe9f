import { defineStore } from "pinia";
export const useOrdersStore = defineStore("orders", () => {
  const { fetchListSellOrder, createOrderTemp, fetchListSellOrderReturn } =
    useOrder();
  const dataListOrder = ref();
  const isAlert = ref(false);
  const getListDataOrder = async (data: any) => {
    dataListOrder.value = [];
    isAlert.value = false;
    try {
      const response = await fetchListSellOrder(data);
      dataListOrder.value = response.data?.data;
      if (response.data?.data.length > 0) {
        isAlert.value = false;
      } else {
        isAlert.value = true;
      }
    } catch (error) {
      throw error;
    }
  };
  //fetchListSellOrderReturn
  const getListDataReturnOrder = async (data: any) => {
    dataListOrder.value = [];
    isAlert.value = false;
    try {
      const response = await fetchListSellOrderReturn(data);
      console.log("response", response);
      dataListOrder.value = response.data?.data;

      if (response.data?.data.length > 0) {
        isAlert.value = false;
      } else {
        isAlert.value = true;
      }
    } catch (error) {
      throw error;
    }
  };
  //
  const searchOrder = async (data: any) => {
    try {
      const response = await fetchListSellOrder(data);
      dataListOrder.value = response.data?.data;

      // if (response.data?.data.length > 0) {
      // }
      return response?.data;
    } catch (error) {
      throw error;
    }
  };
  const addOrder = async (data: any) => {
    if (!Array.isArray(dataListOrder.value)) {
      dataListOrder.value = [];
    }
    dataListOrder.value = [...dataListOrder.value, data];
  };
  const tooltip = ref<any>();
  const handleToogleTootip = (id: string) => {
    if (tooltip.value) {
      if (tooltip.value === id) {
        tooltip.value = null;
      } else {
        tooltip.value = id;
      }
    } else {
      tooltip.value = id;
    }
  };
  //
  const listOrderComplete = ref<any>([]);
  const addListOrderComplete = (order: any) => {
    if (
      order.financialStatusDescription === "Đã thanh toán" &&
      order.status === "APPROVED"
    ) {
      listOrderComplete.value.push(order);
    }
  };
  const removeListOrderComplete = (order: any) => {
    listOrderComplete.value = listOrderComplete.value.filter(
      (item: any) => item.id !== order.id
    );
    console.log("mảng sau khi xóa", listOrderComplete.value);
  };
  const isCheckAllFfm = ref(false);
  ////////////////
  const dataListOrderComplete = ref();
  const getListDataOrderComplete = async (data: any) => {
    dataListOrderComplete.value = [];
    isAlert.value = false;
    try {
      const response = await fetchListSellOrder(data);
      dataListOrderComplete.value = response.data?.data;
      if (response.data?.data.length > 0) {
        isAlert.value = false;
      } else {
        isAlert.value = true;
      }
    } catch (error) {
      throw error;
    }
  };
  const addOrderComplete = async (data: any) => {
    if (!Array.isArray(dataListOrderComplete.value)) {
      dataListOrderComplete.value = [];
    }
    dataListOrderComplete.value = [...dataListOrderComplete.value, data];
  };
  const upDateCancelOrderStatus = (order: any, reason: string) => {
    const index = dataListOrder.value.findIndex(
      (orders: any) => orders.id === order.id
    );
    if (index !== -1) {
      const updatedOrder = {
        ...dataListOrder.value[index],
        status: "CANCELLED",
        statusDescription: "Đã hủy",
        order: {
          ...dataListOrder.value[index]?.order,
          note: reason, // Gán lý do hủy vào note của order
        },
      };
      dataListOrder.value.splice(index, 1, updatedOrder);
    }
  };
  const updateQuantityPrintOrder = (order: any) => {
    const index = dataListOrder.value.findIndex(
      (orders: any) => orders.id === order.id
    );

    if (index !== -1) {
      const customAttributes =
        dataListOrder.value[index].order.customAttributes;
      const printTimesAttr = customAttributes.find(
        (attr: any) => attr.key === "printTimes"
      );

      if (printTimesAttr) {
        // Nếu đã có `printTimes`, tăng giá trị lên 1
        printTimesAttr.value = String(Number(printTimesAttr.value) + 1);
      } else {
        // Nếu chưa có, thêm mới
        customAttributes.push({ key: "printTimes", value: "1" });
      }
    }
  };
  const updateFFMStatus = (order: any) => {
    const index = dataListOrder.value.findIndex(
      (orders: any) => orders.id === order.id
    );
    if (index !== -1) {
      const updatedOrder = {
        ...dataListOrder.value[index],
        order: {
          ...dataListOrder.value[index]?.order,
          fulfillmentStatus: "FULFILLED",
        },
      };
      dataListOrder.value.splice(index, 1, updatedOrder);
    }
  };
  return {
    getListDataOrder,
    dataListOrder,
    searchOrder,
    addOrder,
    isAlert,
    tooltip,
    handleToogleTootip,
    listOrderComplete,
    addListOrderComplete,
    removeListOrderComplete,
    isCheckAllFfm,
    getListDataOrderComplete,
    dataListOrderComplete,
    addOrderComplete,
    upDateCancelOrderStatus,
    updateQuantityPrintOrder,
    getListDataReturnOrder,
    updateFFMStatus,
  };
});
