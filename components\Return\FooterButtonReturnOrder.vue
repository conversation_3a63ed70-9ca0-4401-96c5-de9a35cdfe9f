<template>
  <div>
    <div class="flex items-center justify-center mx-2">
      <div
        @click="handleCreateOrderReturn"
        class="bg-primary text-white w-full text-center py-2 rounded cursor-pointer"
      >
        <PERSON><PERSON><PERSON> trả hàng
      </div>
    </div>
    <div v-if="isLoading">
      <LoadingSpinner />
    </div>
    <!--  -->
    <PopupReturnOrder
      v-if="isOpenPopup"
      :dataReturnOrder="dataReturnOrder"
      :isSwapOrder="isSwapOrder"
    ></PopupReturnOrder>
  </div>
</template>
<script setup>
const returnStore = returnOrderStore();
const totalProductReturn = computed(() => returnStore.totalProductReturn);
const totalPriceProductReturn = computed(
  () => returnStore.totalPriceProductReturn
);
const feeReturnOrder = computed(() => returnStore.feeReturnOrder);
const orderReturn = computed(() => returnStore.orderReturn);
const orderChooseReturn = computed(() => returnStore.orderChooseReturn);
const employeeCreateOrderReturn = computed(
  () => returnStore.employeeCreateOrderReturn
);
const orderStore = useOrderStore();
const orderDetail = computed(() => orderStore.orderDetail);
const paymentMethodReturnOrder = computed(
  () => returnStore.paymentMethodReturnOrder
);
const noteOrderReturn = computed(() => returnStore.noteOrderReturn);
const {
  createOrderReturn,
  updateExchangeOrder,
  openPaymentByOrderId,
  updateStatusApproved,
  updateStatusReturnOrder,
} = useOrder();

const { createPaymentOrder } = usePayment();
const isLoading = ref(false);
const route = useRoute();
const dataReturnOrder = ref();
const isOpenPopup = ref(false);
const isSwapOrder = ref(false);
const handleCreateOrderReturn = async () => {
  const auth = useCookie("auth").value;
  if (orderChooseReturn.value?.length === 0) {
    useNuxtApp().$toast.warning("Vui lòng chọn sản phẩm trả");
    return;
  }
  if (
    orderDetail.value &&
    orderDetail.value?.activeOrderItemProfiles?.length > 0
  ) {
    if (
      orderDetail.value?.remainTotal -
        (+totalPriceProductReturn.value - +feeReturnOrder.value) <
      0
    ) {
      useNuxtApp().$toast.warning("Tiền đổi hàng phải lớn hơn tiền trả hàng");
      return;
    }
  }
  // tạo mảng item trả hàng
  const dataListItem = orderChooseReturn.value.map((item) => ({
    productId: item.orderLineItem.variant.id,
    sku: item?.orderLineItem?.variant?.sku || "",
    title: item?.orderLineItem?.variant?.title,
    quantity: item?.orderLineItem?.currentQuantity,
    unitPrice: {
      amount: item?.orderLineItem?.realPriceSell?.amount,
      currencyCode: "VND",
    },
    totalPrice: {
      amount:
        item?.orderLineItem?.realPriceSell?.amount *
        item?.orderLineItem?.currentQuantity,
      currencyCode: "VND",
    },
  }));
  // data trả hàng
  const data = {
    saleOrderId: orderReturn.value?.id,
    saleId: employeeCreateOrderReturn.value?.id,
    customerId: orderReturn.value?.order?.ownerPartyId,
    customerName: orderReturn.value?.order?.ownerName,
    customerPhone: orderReturn.value?.order?.ownerPhone,
    customerEmail: orderReturn.value?.order?.ownerEmail,
    pickupAddress: {
      name: orderReturn.value?.order?.ownerName,
      phone: orderReturn.value?.order?.ownerPhone,
      provinceGeoId: orderReturn.value?.order?.shippingAddress?.provinceCode,
      districtGeoId: orderReturn.value?.order?.shippingAddress?.districtCode,
      wardGeoId: orderReturn.value?.order?.shippingAddress?.wardCode,
      provinceName: orderReturn.value?.order?.shippingAddress?.province,
      districtName: orderReturn.value?.order?.shippingAddress?.district,
      wardName: orderReturn.value?.order?.shippingAddress?.ward,
      primary: false,
      address: orderReturn.value?.order?.shippingAddress?.address1,
    },
    carrierId: null,
    shippingServiceId: null,
    storeId: orderReturn.value?.order?.shopId,
    warehouseId: orderReturn.value?.order?.customAttribute?.facilityId,
    platform: "pos",
    note: noteOrderReturn.value,
    items: dataListItem,
    orderDate: new Date().toISOString(),
    orderType: "POS_RETURN",
    // subTotalPrice: {
    //   amount: 500000,
    //   currencyCode: "VND",
    // },
    // totalPrice: {
    //   amount: 500000,
    //   currencyCode: "VND",
    // },
    feeReturn: {
      amount: feeReturnOrder.value,
      currencyCode: "VND",
    },
    // feeShipping: {
    //   amount: 0,
    //   currencyCode: "VND",
    // },
  };
  try {
    isLoading.value = true;
    // flow tạo đơn trả hàng: -> data trả về là đơn mới -> lấy đơn mới đó tạo payment hoàn tiền.
    const response = await createOrderReturn(data, auth?.user?.id);
    if (response?.status === 0) {
      return;
    }

    // tạo thanh toán cho đơn trả
    const dataPayment = {
      orderId: response?.data,
      paymentMethod:
        orderDetail.value &&
        orderDetail.value?.activeOrderItemProfiles?.length > 0
          ? "clearing_debt"
          : "refund",
      appliedAmount: +totalPriceProductReturn.value - +feeReturnOrder.value,
      payDate: Date.now(),
      source: "ORDER_SOURCE",
      paymentType: "ONLINE",
      createBy: auth?.user?.id,
    };
    const resPayment = await createPaymentOrder(dataPayment);
    await updateStatusReturnOrder(response?.data, "APPROVED", "");
    // đến đơn đổi
    // Đơn trả response?.data
    // Đươn đổi orderDetail?.id
    // sell order: route.query.orderId
    // flow tạo đơn đổi liên kết đơn đổi + cập nhật số tiền còn nợ + updateStatus
    // liên kết đơn đổi
    
    // Kiểm tra nếu có đổi hàng
    if (
      orderDetail.value &&
      orderDetail.value?.activeOrderItemProfiles?.length > 0
    ) {
      await updateExchangeOrder(
        orderDetail.value?.id,
        response?.data,
        route.query?.orderReturnId
      );
      await updateStatusApproved(orderDetail.value?.id);
      // cập nhật tiền còn nợ đơn đổi
      const dataPaymentSwap = {
        orderId: orderDetail.value?.id,
        paymentMethod: "CLEARING_DEBT",
        appliedAmount: +totalPriceProductReturn.value - +feeReturnOrder.value,
        payDate: Date.now(),
        source: "ORDER_SOURCE",
        paymentType: paymentMethodReturnOrder.value?.type,
        createBy: auth?.user?.id,
        paymentInfo: `đơn cấn trừ từ đơn ${orderReturn.value?.id}`,
      };
      await createPaymentOrder(dataPaymentSwap);
      // chuyển đến trang thanh toán để thanh toán
      isSwapOrder.value = true;
      // openPaymentByOrderId(orderDetail.value?.id);
    }
    isOpenPopup.value = true;
    dataReturnOrder.value = response?.data;
  } catch (error) {
    throw error;
  } finally {
    isLoading.value = false;
  }
};
const router = useRouter();
router.beforeEach(async (to, from, next) => {
  if (to.path === `/order/return`) {
    isSwapOrder.value = false;
  }
  next();
});
</script>
