import { defineStore } from "pinia";
export const useCustomerStore = defineStore("customer", () => {
  const { fetchListSellOrderAll, getShippingInfo } = useOrder();
  const {
    getCustomerById,
    fetchListCustomer,
    getCustomerWallet,
    getTopicByCustomerId,
  } = useCustomer();

  const customer = ref();
  const listOrder = ref();
  const shippingAddress = ref<any>([]);
  const listCustomer = ref<any>([]);
  const handleCusTomer = async (customerId: string) => {
    listOrder.value = [];
    try {
      const requestData = {
        currentPage: 1,
        customer_multi_value: customerId,
        status_ignore: [1],
      };
      const [customerInfo, response2, shippingInfo] = await Promise.all([
        handleGetCustomer(customerId),
        fetchListSellOrderAll(requestData),
        handleGetShippingInfo(customerId),
      ]);

      listOrder.value = response2.data?.data;
    } catch (error) {
      throw error;
    }
  };
  const handleGetShippingInfo = async (customerId: string) => {
    try {
      shippingAddress.value = [];
      const response = await getShippingInfo(customerId);
      shippingAddress.value = response.data;
    } catch (error) {
      throw error;
    }
  };
  const handleGetCustomer = async (customerId: string) => {
    try {
      customer.value = [];
      const response = await getCustomerById(customerId);
      customer.value = response;
    } catch (error) {
      throw error;
    }
  };
  const handleAddCustomer = (data: any) => {
    if (!Array.isArray(listCustomer.value)) {
      listCustomer.value = [];
    }
    listCustomer.value = [...listCustomer.value, ...data];
  };
  const handleAddCustomerFirst = (data: any) => {
    if (!Array.isArray(listCustomer.value)) {
      listCustomer.value = [];
    }
    if (listCustomer.value?.length > 0) {
      listCustomer.value = [...data, ...listCustomer.value];
    }
  };
  const listCustomerAction = ref<any>([]);
  const addListCustomerAction = (customer: any) => {
    listCustomerAction.value.push(customer);
  };
  const removeCustomerListAction = (customer: any) => {
    listCustomerAction.value = listCustomerAction.value.filter(
      (item: any) => item.id !== customer.id
    );
  };
  const isCheckGlobal = ref(false);
  const totalPages = ref<any>();
  const loading = ref(false);
  const handleGetListCustomer = async (data: any) => {
    try {
      loading.value = true;
      const response: any = await fetchListCustomer(data);
      listCustomer.value = response.content;
      loading.value = false;
    } catch (error) {
      loading.value = false;
      throw error;
    }
  };
  const listTopic = ref();
  const handleGetTopic = async (customerId: string) => {
    try {
      const response = await getTopicByCustomerId(
        { customerId: customerId },
        10,
        1
      );
      listTopic.value = response?.content;
    } catch (error) {
      throw error;
    }
  };
  const handleUpdateShippingAddressDefault = (shippingAddressId: string) => {
    shippingAddress.value = shippingAddress.value?.map((address: any) => ({
      ...address,
      address_default: address.id === shippingAddressId,
    }));
  };
  const handleUpdateShippingAddress = async (
    shippingAddressNew: any,
    customerId: string
  ) => {
    const response = await getShippingInfo(customerId);
    const shppingAdd = response?.data.find(
      (address: any) => address.id === shippingAddressNew.id
    );
    shippingAddress.value = shippingAddress.value.map((address: any) =>
      address.id === shppingAdd.id ? shppingAdd : address
    );
    handleUpdateShippingAddressDefault(shppingAdd?.id);
  };

  return {
    handleCusTomer,
    customer,
    listOrder,
    shippingAddress,
    handleGetShippingInfo,
    handleGetCustomer,
    listCustomer,
    handleAddCustomer,
    addListCustomerAction,
    listCustomerAction,
    removeCustomerListAction,
    isCheckGlobal,
    handleGetListCustomer,
    totalPages,
    loading,
    handleAddCustomerFirst,
    handleGetTopic,
    listTopic,
    handleUpdateShippingAddressDefault,
    handleUpdateShippingAddress,
  };
});
