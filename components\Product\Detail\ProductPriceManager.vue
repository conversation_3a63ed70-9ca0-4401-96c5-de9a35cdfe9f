<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center gap-3">
        <div
          class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-green-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
            />
          </svg>
        </div>
        <div>
          <h2 class="text-lg font-semibold text-gray-900">Thông tin gi<PERSON> bán</h2>
          <p class="text-sm text-gray-500">Quản lý giá bán và giá khuyến mãi</p>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="p-2 space-y-2">
      <!-- Price Fields -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Regular Price -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Giá bán
            <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <input
              type="text"
              v-model="formattedPrice"
              class="w-full pl-3 pr-12 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
              placeholder="0"
              @blur="handleChangePrice"
              @input="handlePriceInput"
            />
            <div
              class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"
            >
              <span class="text-gray-500 text-sm">₫</span>
            </div>
          </div>
        </div>

        <!-- Promotion Price -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Giá khuyến mãi
          </label>
          <div class="relative">
            <input
              type="text"
              v-model="formattedPricePromotion"
              class="w-full pl-3 pr-12 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
              placeholder="0"
              @blur="handleChangePricePromotion"
              @input="handlePromotionPriceInput"
            />
            <div
              class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"
            >
              <span class="text-gray-500 text-sm">₫</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Price Summary -->
      <div v-if="price > 0" class="bg-gray-50 rounded-lg p-4">
        <h3 class="text-sm font-medium text-gray-900 mb-3">Tóm tắt giá</h3>
        <div class="space-y-2">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Giá gốc:</span>
            <span class="text-sm font-medium">{{ formatCurrency(price) }}</span>
          </div>
          <div
            v-if="pricePromotion > 0"
            class="flex justify-between items-center"
          >
            <span class="text-sm text-gray-600">Giá khuyến mãi:</span>
            <span class="text-sm font-medium text-green-600">{{
              formatCurrency(pricePromotion)
            }}</span>
          </div>
          <div
            v-if="pricePromotion > 0 && price > pricePromotion"
            class="flex justify-between items-center border-t pt-2"
          >
            <span class="text-sm text-gray-600">Tiết kiệm:</span>
            <span class="text-sm font-medium text-red-600"
              >{{ formatCurrency(price - pricePromotion) }} ({{
                discountPercentage
              }}%)</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
interface Props {
  product: any;
}

const props = defineProps<Props>();
const emit = defineEmits(["update"]);

const price = ref<number>(0);
const pricePromotion = ref<number>(0);
const formattedPrice = ref<string>("");
const formattedPricePromotion = ref<string>("");

// Watch for product changes
watch(
  () => props.product,
  (newVal) => {
    if (newVal) {
      if (newVal.compareAtPrice) {
        price.value = newVal.compareAtPrice;
        pricePromotion.value = newVal.price;
      } else {
        price.value = newVal.price || 0;
        pricePromotion.value = 0;
      }

      // Update formatted values
      formattedPrice.value = formatCurrency(price.value);
      formattedPricePromotion.value = formatCurrency(pricePromotion.value);
    }
  },
  { immediate: true }
);

const auth = useCookie<any>("auth");
const { updatePrice, updatePricePromotion } = useProduct();
const parseCurrency = (value: string): number => {
  if (!value || value.trim() === "") return 0;
  return parseInt(value.replace(/[^\d]/g, "")) || 0;
};

// Computed properties
const discountPercentage = computed(() => {
  if (
    price.value >= 0 &&
    pricePromotion.value >= 0 &&
    price.value > pricePromotion.value
  ) {
    return Math.round(
      ((price.value - pricePromotion.value) / price.value) * 100
    );
  }
  return 0;
});

// Input handlers
const handlePriceInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const rawValue = target.value;

  // Allow empty input
  if (rawValue === "") {
    price.value = 0;
    return;
  }

  const numericValue = parseCurrency(rawValue);
  price.value = numericValue;

  // Update formatted display
  formattedPrice.value = formatCurrency(numericValue);
};

const handlePromotionPriceInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const rawValue = target.value;

  // Allow empty input
  if (rawValue === "") {
    pricePromotion.value = 0;
    return;
  }

  const numericValue = parseCurrency(rawValue);
  pricePromotion.value = numericValue;

  // Update formatted display
  formattedPricePromotion.value = formatCurrency(numericValue);
};

// Update handlers
const handleChangePrice = async () => {
  try {
    await updatePrice(
      props.product?.id,
      price.value.toString(),
      auth.value?.user?.id
    );
    emit("update");
  } catch (error) {
    console.error("Error updating price:", error);
  }
};

const handleChangePricePromotion = async () => {
  try {
    await updatePricePromotion(
      props.product?.id,
      pricePromotion.value.toString(),
      auth.value?.user?.id
    );
    emit("update");
  } catch (error) {
    console.error("Error updating promotion price:", error);
  }
};
</script>
