<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div class="text-center">
      <!-- Avatar -->
      <div
        class="w-16 h-16 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-4"
      >
        <span class="text-2xl font-bold text-black">{{ userInitial }}</span>
      </div>

      <!-- User Info -->
      <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ userName }}</h3>
      <p class="text-sm text-gray-600 mb-1">{{ userRole }}</p>
      <p class="text-xs text-gray-500 mb-4">Mã NV: {{ userCode }}</p>

      <!-- Status -->
      <div
        v-if="textTimekeeping === 'Check In'"
        class="flex items-center justify-center gap-1 mb-6 bg-green-200 p-2 rounded"
      >
        <span class="text-green-400">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"
            />
          </svg>
        </span>
        <span class="text-sm text-green-400">{{ userStatus }}</span>
      </div>
      <div
        v-else
        class="flex items-center justify-center gap-1 mb-6 bg-red-200 p-2 rounded"
      >
        <span class="text-red-400">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg>
        </span>
        <span class="text-sm text-red-400">{{ userStatus }}</span>
      </div>
      <!-- Check In Button -->
      <button
        @click="toogleTimekeeping"
        class="w-full bg-green-500 hover:bg-green-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
      >
        <svg
          class="w-4 h-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
          />
        </svg>
        {{ textTimekeeping }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import type { Auth } from "~/types/Auth";

// Props
interface Props {
  auth?: Auth;
}

const props = withDefaults(defineProps<Props>(), {
  auth: undefined,
});

// Emits
const emit = defineEmits<{
  checkIn: [];
}>();

// Computed properties
const userName = computed(() => props.auth?.user?.name || "Nhân viên");

const userInitial = computed(() => {
  const name = props.auth?.user?.name || "N";
  return name.charAt(0).toUpperCase();
});

const userRole = computed(() => {
  const roles = props.auth?.user?.roles || [];
  if (roles.includes("ORG_ADMIN")) return "Quản trị viên";
  if (roles.includes("SALE_MANAGER")) return "Quản lý bán hàng";
  if (roles.includes("SALE")) return "Nhân viên bán hàng";
  return "Nhân viên";
});

const userCode = computed(() => props.auth?.user?.id?.slice(-6) || "NV001");

const userStatus = ref("Đang làm việc");
const textTimekeeping = ref("Check In");
const toogleTimekeeping = () => {
  if (textTimekeeping.value === "Check In") {
    textTimekeeping.value = "Check Out";
    userStatus.value = "Đã về";
  } else {
    textTimekeeping.value = "Check In";
    userStatus.value = "Đang làm việc";
  }
};
</script>
