// plugins/service-worker.client.ts
export default defineNuxtPlugin(() => {
  if ("serviceWorker" in navigator) {
    window.addEventListener("load", () => {
      navigator.serviceWorker
        .register("/sw.js")
        .then((registration) => {
          console.log("Service Worker đã đăng ký:", registration);
        })
        .catch((error) => {
          console.error("Đăng ký Service Worker thất bại:", error);
        });
    });
  }
});
