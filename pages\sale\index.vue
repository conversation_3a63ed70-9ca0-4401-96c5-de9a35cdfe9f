<template>
  <div>
    <Teleport to="body">
      <LoadingSpinner
        v-if="orderStore.isLoadingPayment || isLoad"
        text="Đang xử lý"
        subText="Vui lòng đợi trong giây lát..."
      />
    </Teleport>
    <div
      class="h-full overflow-y-scroll md:overflow-hidden"
      :class="{ 'opacity-50 pointer-events-none': isLoad }"
    >
      <div class="items-center gap-[10px] mt-2 px-2"></div>
      <div class="w-full relative h-full text-sm">
        <div class="wrapper lg:my-0 my-0 px-2 md:[px-15] h-full">
          <div class="w-full relative h-full-custom">
            <!-- phần tab -->
            <div class="hidden md:block">
              <TabOrder :isLoad="isLoad"></TabOrder>
            </div>
            <div class="hidden md:block">
              <LoadingPageSale v-if="isLoad"></LoadingPageSale>
            </div>
            <div
              class="grid grid-cols-1 md:grid-cols-3 gap-2 md:mb-4 md:h-full z-0"
            >
              <div
                class="w-full col-span-2 relative bg-white rounded h-full md:order-1"
              >
                <div class="relative md:h-screen-100">
                  <OrderHeader
                    :orderDetail="orderDetail"
                    :invoice="invoice"
                    :isMobile="true"
                    @toggleExportInvoice="toogleExportInvoice"
                  />
                  <!-- search product -->
                  <SearchProduct></SearchProduct>
                  <!-- Danh sách sản phẩm -->
                  <OrderItems :isTrue="true"></OrderItems>
                  <!-- chiến dịch + lịch sử thanh toán -->
                  <div class="md:block hidden">
                    <div class="absolute bottom-0 w-full">
                      <div v-if="campaign" class="border-t">
                        <Campaign></Campaign>
                      </div>
                      <HistoryPaymentPopup
                        v-if="
                          orderDetail &&
                          (orderDetail.status === 'APPROVED' ||
                            orderDetail.status === 'COMPLETED')
                        "
                        :orderDetail="orderDetail"
                      ></HistoryPaymentPopup>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="w-full col-span-2 md:col-span-1 order-1 md:order-2 mb-24"
              >
                <div
                  class="flex flex-col gap-1 md:overflow-y-scroll md:h-screen-200 bg-white"
                >
                  <OrderHeader
                    :orderDetail="orderDetail"
                    :invoice="invoice"
                    :isMobile="false"
                    @toggleExportInvoice="toogleExportInvoice"
                  />
                  <!-- Phần khách hàng -->
                  <div class="border-b pb-1">
                    <UserDetail v-if="customer"></UserDetail>
                    <SearchUser v-else></SearchUser>
                  </div>
                  <!-- Chiến dịch giảm giá -->
                  <div class="block md:hidden">
                    <div v-if="campaign" class="border-b pb-1">
                      <Campaign></Campaign>
                    </div>
                  </div>
                  <!-- phần tag -->
                  <div class="border-b pb-1">
                    <TagOrder></TagOrder>
                  </div>
                  <!-- điạ chỉ nhận hàng -->
                  <div class="border-b">
                    <TypeOrderComponent
                      v-if="orderDetail"
                      :customer="customer"
                      :orderDetail="orderDetail"
                      :dataShippingAddress="dataShippingAddress"
                      :dataDefaultAddress="dataDefaultAddress"
                    ></TypeOrderComponent>
                    <div
                      v-else
                      class="mx-2 flex items-center justify-between py-2"
                    >
                      <div class="font-semibold text-sm text-primary">
                        Địa chỉ nhận hàng
                      </div>
                      <span>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke-width="1.5"
                          stroke="currentColor"
                          class="size-5"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="M12 4.5v15m7.5-7.5h-15"
                          />
                        </svg>
                      </span>
                    </div>
                  </div>
                  <div class="border-b pb-1">
                    <InfoPayment></InfoPayment>
                  </div>
                  <div class="border-b pb-1">
                    <EditEmployee></EditEmployee>
                  </div>
                  <HistoryPaymentPopup
                    class="md:hidden"
                    v-if="
                      orderDetail &&
                      (orderDetail.status === 'APPROVED' ||
                        orderDetail.status === 'COMPLETED')
                    "
                    :orderDetail="orderDetail"
                  ></HistoryPaymentPopup>
                  <div class="border-b pb-1">
                    <OrderNoteExtend></OrderNoteExtend>
                  </div>
                  <RelativeOrder
                    v-if="orderDetail?.order?.groupIds?.length > 0"
                    :orderRelate="orderRelate?.relations"
                  >
                  </RelativeOrder>
                </div>
                <FooterOrder
                  @toogleHistoryPayment="toogleHistoryPayment"
                ></FooterOrder>
              </div>
            </div>
          </div>
        </div>
      </div>
      <ModalCreateOrder v-if="orderStore.isAlert"></ModalCreateOrder>
      <WaringWareHouse
        v-if="orderStore.isAlertWareHouse"
        @cancel="orderStore.isAlertWareHouse = false"
      ></WaringWareHouse>
      <ExportInvoicePopup
        v-if="isOpenExportInvoice"
        :order="orderDetail"
        @confirm="toogleExportInvoice"
        @cancel="toogleExportInvoice"
      ></ExportInvoicePopup>
    </div>
  </div>
</template>

<script setup lang="ts">
const LoadingSpinner = defineAsyncComponent({
  loader: () => import("~/components/common/LoadingSpinner.vue"),
  loadingComponent: () =>
    h("div", { class: "animate-pulse bg-gray-200 h-8 w-8 rounded" }),
  errorComponent: () => h("div", "Loading error"),
  delay: 200,
  timeout: 3000,
});

const LoadingPageSale = defineAsyncComponent({
  loader: () => import("~/components/Loading/LoadingPageSale.vue"),
  delay: 200,
});

// Critical components - load immediately
const SearchProduct = defineAsyncComponent(
  () => import("~/components/Sale/Products/SearchProduct.vue")
);
const OrderItems = defineAsyncComponent(
  () => import("~/components/Order/OrderItems.vue")
);

const UserDetail = defineAsyncComponent({
  loader: () => import("~/components/Sale/User/UserDetail.vue"),
  delay: 200,
});

const SearchUser = defineAsyncComponent({
  loader: () => import("~/components/Sale/User/SearchUser.vue"),
  delay: 200,
});

const TagOrder = defineAsyncComponent({
  loader: () => import("~/components/Tag/TagOrder.vue"),
  delay: 300,
});

const TypeOrderComponent = defineAsyncComponent({
  loader: () => import("~/components/Order/TypeOrderComponent.vue"),
  delay: 300,
});

const InfoPayment = defineAsyncComponent({
  loader: () => import("~/components/Order/InfoPayment.vue"),
  delay: 300,
});

const EditEmployee = defineAsyncComponent({
  loader: () => import("~/components/Order/EditEmployee.vue"),
  delay: 400,
});

const RelativeOrder = defineAsyncComponent({
  loader: () => import("~/components/Order/RelativeOrder.vue"),
  delay: 400,
});

const FooterOrder = defineAsyncComponent({
  loader: () => import("~/components/Order/FooterOrder.vue"),
  delay: 200,
});

// Modal components - only load when needed
const ModalCreateOrder = defineAsyncComponent({
  loader: () => import("~/components/Modal/ModalCreateOrder.vue"),
  delay: 100,
});

const WaringWareHouse = defineAsyncComponent({
  loader: () => import("~/components/dialog/WaringWareHouse.vue"),
  delay: 100,
});

const ExportInvoicePopup = defineAsyncComponent({
  loader: () => import("~/components/dialog/ExportInvoicePopup.vue"),
  delay: 100,
});
const route = useRoute();
const { getOrderById, getDataShippingAddress } = useOrderStore();
const orderStore = useOrderStore();

// Optimized computed properties with better caching
const customer = computed(() => orderStore.customerInOrder);
const campaign = computed(() => orderStore.campaign);
const invoice = computed<any>(() => orderStore.dataInvoice);

// Loading states
const isLoad = ref<boolean>(false);
const isInitialLoad = ref<boolean>(true);

// Cache for API responses to avoid duplicate calls
const apiCache = reactive({
  campaignGeneral: null as any,
  campaignCustomer: new Map(),
  orderRelations: new Map(),
  invoiceData: new Map(),
  shippingAddress: new Map(),
});
useHead({
  title: "Bán hàng",
  meta: [
    {
      name: "description",
      content: "Bán hàng",
    },
  ],
});

definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  permission: ["ORG_ADMIN", "SALE", "SALES", "SALE_ADMIN"],
  name: "Bán hàng",
});

const tagStore = useTagStore();
const orderDetail = computed(() => orderStore.orderDetail);
const dataShippingAddress = computed(() => orderStore.dataShippingAddress);
const dataDefaultAddress = computed(() => orderStore.dataDefaultAddress);
const { fetchListOrderRelations } = useOrder();
const orderRelate = ref();

// Optimized helper functions with caching
const getCampaignData = async (customerId?: string) => {
  if (customerId && apiCache.campaignCustomer.has(customerId)) {
    return apiCache.campaignCustomer.get(customerId);
  }

  if (!customerId && apiCache.campaignGeneral) {
    return apiCache.campaignGeneral;
  }

  try {
    const response = await orderStore.handleGetCampaignActiveNow(
      "",
      customerId || ""
    );

    if (customerId) {
      apiCache.campaignCustomer.set(customerId, response);
    } else {
      apiCache.campaignGeneral = response;
    }

    return response;
  } catch (error) {
    console.error("Error fetching campaign data:", error);
    throw error;
  }
};

const getRelateOrder = async () => {
  const orderId = orderDetail.value?.id;
  if (!orderId) return;

  if (apiCache.orderRelations.has(orderId)) {
    orderRelate.value = apiCache.orderRelations.get(orderId);
    return;
  }

  try {
    const response = await fetchListOrderRelations([orderId]);
    const data = response?.data[0];

    apiCache.orderRelations.set(orderId, data);
    orderRelate.value = data;

    return response;
  } catch (error) {
    console.error("Error fetching related orders:", error);
    throw error;
  }
};
//

// Optimized data loading functions
const loadOrderData = async (orderId: string) => {
  const promises = [
    getOrderById(orderId),
    getCampaignData(), // Use cached campaign data
    tagStore.handleGetConnectorByResource(orderId, "ORDER", "TAG"),
  ];

  await Promise.all(promises);
};

const loadAdditionalOrderData = async () => {
  const promises = [];

  // Load related orders if needed
  if (orderDetail.value?.order?.groupIds?.length > 0) {
    promises.push(getRelateOrder());
  }

  // Load invoice data if needed
  if (
    orderDetail.value?.order?.customAttribute?.exportVatInvoiceStatus ===
    "INVOICE_PUBLISHED"
  ) {
    const orderId = orderDetail.value.id;
    if (!apiCache.invoiceData.has(orderId)) {
      promises.push(
        orderStore.handleGetInvoiceOfOrder().then((data) => {
          apiCache.invoiceData.set(orderId, data);
          return data;
        })
      );
    }
  }

  // Load customer-specific data if customer exists
  if (customer.value?.id) {
    const customerId = customer.value.id;

    const customerPromises = [
      // Cache shipping address data
      apiCache.shippingAddress.has(customerId)
        ? Promise.resolve(apiCache.shippingAddress.get(customerId))
        : getDataShippingAddress(customerId).then((data) => {
            apiCache.shippingAddress.set(customerId, data);
            return data;
          }),
      getCampaignData(customerId), // Use cached campaign data for customer
    ];

    promises.push(...customerPromises);
  }

  if (promises.length > 0) {
    await Promise.allSettled(promises);
  }
};

//
onMounted(async () => {
  localStorage.setItem("paymentAmount", "0");

  if (route.query.orderId) {
    isLoad.value = true;

    try {
      // Load core order data first
      await loadOrderData(route.query.orderId as string);

      // Load additional data based on order details
      await loadAdditionalOrderData();
    } catch (error) {
      console.error("Error loading order data:", error);
    } finally {
      isLoad.value = false;
      isInitialLoad.value = false;
    }
  } else {
    // Load general campaign data for new orders
    await getCampaignData();
    isInitialLoad.value = false;
  }
});
const router = useRouter();
router.beforeEach((to, _from, next) => {
  if (to.path === `/sale`) {
    localStorage.setItem("paymentAmount", "0");
  }
  next();
});

// Debounced watcher for route changes to prevent rapid API calls
const debouncedOrderIdWatch = useDebounceFn(async (newOrderId: string) => {
  if (!newOrderId) return;

  isLoad.value = true;

  try {
    // Use optimized loading functions
    await loadOrderData(newOrderId);
    await loadAdditionalOrderData();
  } catch (error) {
    console.error("Error loading order data in watcher:", error);
  } finally {
    isLoad.value = false;
  }
}, 300);

watch(
  () => route.query.orderId,
  async (newVal) => {
    if (newVal && typeof newVal === "string") {
      await debouncedOrderIdWatch(newVal);
    }
  }
);

// Optimized order detail watcher
watch(
  () => orderDetail.value,
  async (newVal) => {
    if (newVal && !isInitialLoad.value) {
      // Only perform additional actions if needed
      // Remove console.log for production
      // console.log("Order detail updated:", newVal.id);
    }
  }
);
// ✅ Order Header Component
const OrderHeader = defineAsyncComponent({
  loader: () => import("~/components/Order/OrderHeader.vue"),
  delay: 200,
  timeout: 3000,
  loadingComponent: () =>
    h("div", { class: "animate-pulse bg-gray-200 h-12 w-full rounded" }),
  errorComponent: () =>
    h("div", { class: "text-red-500 p-2" }, "Failed to load order header"),
});
const isOpenHistoryPopup = ref(false);
const toogleHistoryPayment = () => {
  isOpenHistoryPopup.value = !isOpenHistoryPopup.value;
};
const isOpenExportInvoice = ref(false);
const toogleExportInvoice = () => {
  isOpenExportInvoice.value = !isOpenExportInvoice.value;
};
</script>

<style scoped>
.h-full-custom {
  height: calc(100vh - 4rem);
}
.h-full-custom-footer {
  height: calc(100vh - 25rem);
}
</style>
