import pkg from "./package.json";
import { createResolver } from "@nuxt/kit";

const { resolve } = createResolver(import.meta.url);
export default defineNuxtConfig({
  //enable ssr
  ssr: false,

  app: {
    head: {
      titleTemplate: `%s | ${process.env.SITE_TITLE ?? "POS System"}`,
      htmlAttrs: { lang: "vi" },
      meta: [
        { charset: "utf-8" },
        { name: "viewport", content: "width=device-width, initial-scale=1" },
        { name: "description", content: "Hệ thống POS tối ưu cho bán hàng" },
      ],
      link: [{ rel: "icon", href: "/favicon.png", type: "image/svg+xml" }],
    },
    // pageTransition: { name: "fade", mode: "out-in", appear: true },
    pageTransition: false,
  },

  components: [{ path: resolve("./components"), pathPrefix: false }],

  modules: [
    "@nuxtjs/tailwindcss",
    "nuxt-icon",
    "@nuxt/image",
    "@vueuse/nuxt",
    "@pinia/nuxt",
    "@pinia-plugin-persistedstate/nuxt",
    "nuxt-swiper",
    "@vite-pwa/nuxt",
  ],
  pwa: {
    mode: "production",
    strategies: "generateSW",
    registerType: "autoUpdate",
    manifest: {
      name: "SalePoint",
      short_name: "SalePoint",
      description: "salePoint",
      icons: [
        {
          src: "/images/1024.png",
          sizes: "64x64",
          type: "image/png",
        },
        {
          src: "/images/1024.png",
          sizes: "144x144",
          type: "image/png",
        },
        {
          src: "/images/1024.png",
          sizes: "512x512",
          type: "image/png",
        },
      ],
    },
    workbox: {
      globPatterns: ["**/*.{js,css,html,png,svg,ico}"],
      maximumFileSizeToCacheInBytes: 10 * 1024 * 1024,
      // Thêm navigateFallback để xử lý URL gốc
      navigateFallback: "/index.html",
      // Đảm bảo precache các tệp HTML
      additionalManifestEntries: [
        { url: "/index.html", revision: Date.now().toString() },
        { url: "/", revision: Date.now().toString() },
      ],
      cleanupOutdatedCaches: true, // Xóa cache cũ khi kích hoạt service worker mới
      clientsClaim: true, // Kiểm soát client ngay lập tức
      skipWaiting: true, // Bỏ qua chờ đợi, kích hoạt service worker mới ngay
      // Tùy chỉnh runtime caching cho các tài nguyên động
      runtimeCaching: [
        {
          urlPattern: /^\/_nuxt\/.*\.(js|css)$/,
          handler: "CacheFirst",
          options: {
            cacheName: "nuxt-assets",
            expiration: {
              maxEntries: 100,
              maxAgeSeconds: 30 * 24 * 60 * 60, // 30 ngày
            },
          },
        },
        {
          urlPattern: /^\/.*/, // Xử lý tất cả các route
          handler: "NetworkFirst",
          options: {
            cacheName: "dynamic-routes",
            expiration: {
              maxEntries: 50,
              maxAgeSeconds: 30 * 24 * 60 * 60, // 30 ngày
            },
          },
        },
      ],
    },
    client: {
      installPrompt: true,
    },
    devOptions: {
      enabled: true,
      suppressWarnings: true,
      navigateFallback: "/",
      navigateFallbackAllowlist: [/^\/$/],
    },
  },
  runtimeConfig: {
    public: {
      version: pkg.version || "0.0.0",
      LOGO: "/images/logo-lvs.png",
      ENV_NAME: process.env.ENV_NAME,
      MATRIX_SSO: process.env.MATRIX_SSO,
      MATRIX_LINK: process.env.MATRIX_LINK,
    },
  },

  css: ["/assets/css/main.css"],
  plugins: [
    { src: "~/plugins/sdk.ts" }, // chạy cả server + client
    { src: "~/plugins/toastify.ts", mode: "client" },
    { src: "~/plugins/tippy.ts", mode: "client" },
    { src: "~/plugins/matrix.client.ts", mode: "client" }, // nếu chỉ dùng cho UI chat
  ],
  devtools: { enabled: true },
  vite: {
    define: {
      global: "window",
    },

    build: {
      minify: "esbuild",
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ["vue", "pinia"],
          },
        },
      },
    },
  },
  // Build optimization
  nitro: {
    compressPublicAssets: true,
    minify: true,
    experimental: {
      wasm: true,
    },
  },
  image: {
    format: ["webp", "avif"],
  },
});
