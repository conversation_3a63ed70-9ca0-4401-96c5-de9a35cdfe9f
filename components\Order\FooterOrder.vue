<template>
  <div
    class="fixed bottom-0 md:relative left-0 right-0 bg-white p-2 shadow md:rounded"
  >
    <div class="flex w-full items-center">
      <div class="md:block hidden w-full">
        <div
          v-if="
            !(orderDetail?.financialStatusDescription === 'Đã thanh toán') &&
            orderDetail?.status !== 'CANCELLED'
          "
          class="grid grid-cols-2 w-full gap-4 items-center"
        >
          <div
            @click="handleOpenPartialPayment"
            class="border border-primary text-primary w-full text-center px-2 py-1 rounded cursor-pointer"
          >
            Thanh toán một phần
          </div>
          <div
            @click="orderStore.handlePlaceOrder()"
            class="bg-primary border border-primary text-center text-white w-full rounded px-2 py-1 cursor-pointer"
          >
            Thanh toán
          </div>
        </div>
        <!-- <div v-else class="w-full gap-4 items-center">
          <button
            v-if="orderDetail?.financialStatusDescription === 'Đã thanh toán'"
            @click="tooglePaymentHistory"
            class="bg-primary border border-primary text-center text-white w-full rounded px-2 py-1 cursor-pointer"
          >
            Lịch sử thanh toán
          </button>
        </div> -->
      </div>

      <!-- mobile -->
      <button
        v-if="
          !(orderDetail?.financialStatusDescription === 'Đã thanh toán') &&
          orderDetail?.status !== 'CANCELLED'
        "
        class="bg-primary text-white py-2 px-4 rounded-lg md:px-2 md:py-1 w-full block md:hidden"
        @click="orderStore.handlePlaceOrder()"
      >
        Thanh toán •
        {{ formatCurrency(totalPrice + orderStore.shippingFee || 0) }}
      </button>
      <!-- <button
        v-if="orderDetail?.financialStatusDescription === 'Đã thanh toán'"
        class="bg-primary text-white py-2 px-4 rounded-lg md:px-2 md:py-1 w-full block md:hidden"
        :class="orderDetail?.status !== 'COMPLETED' ? ' disabled ' : ''"
        @click="tooglePaymentHistoryPopup"
      >
        Đổi trả hàng
      </button> -->
      <button
        v-if="orderDetail?.financialStatusDescription === 'Đã thanh toán'"
        class="bg-white text-white py-2 px-4 rounded-lg md:px-2 md:py-1 w-full block md:hidden"
        :class="orderDetail?.status !== 'COMPLETED' ? ' disabled ' : ''"
      >
      </button>
      <!-- <div
        class="py-2 px-1 cursor-pointer block md:hidden"
        @click="handleOpenPartialPayment"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="size-6"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M12 6.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5Z"
          />
        </svg>
      </div> -->
      <!-- action list -->
      <!-- <ActionFooterOrder :order="orderDetail"></ActionFooterOrder> -->
    </div>
  </div>
  <PartialPaymentMessage
    v-if="isPartialPayment"
    @cancel="handleOpenPartialPayment"
    @confirm="handleConfirmPartialPayment"
  ></PartialPaymentMessage>
  <PaymentPopup
    v-if="isOpenPopup"
    :orderDetail="orderDetail"
    @cancel="tooglePaymentHistoryPopup"
  ></PaymentPopup>
</template>

<script setup lang="ts">
// Lazy load heavy components
const PartialPaymentMessage = defineAsyncComponent(
  () => import("~/components/dialog/PartialPaymentMessage.vue")
);
const ActionFooterOrder = defineAsyncComponent(
  () => import("~/components/Order/ActionFooterOrder.vue")
);
const orderStore = useOrderStore();
const { printOrderHTML } = useOrder();
const totalPrice = computed(() => orderStore.orderDetail?.remainTotal);
const orderDetail = computed(() => orderStore.orderDetail);
const isPartialPayment = ref(false);
const route = useRoute();
const router = useRouter();

const handleOpenPartialPayment = () => {
  orderStore.paymentAmount = 0;
  isPartialPayment.value = !isPartialPayment.value;
};
const handleConfirmPartialPayment = async () => {
  await orderStore.handlePlaceOrder();
  isPartialPayment.value = !isPartialPayment.value;
};
const isOpenHistory = ref(false);
const emits = defineEmits(["toogleHistoryPayment"]);
const tooglePaymentHistory = () => {
  isOpenHistory.value = !isOpenHistory.value;
  emits("toogleHistoryPayment", isOpenHistory.value);
};
const isOpenPopup = ref(false);
const tooglePaymentHistoryPopup = () => {
  isOpenPopup.value = !isOpenPopup.value;
};
</script>
