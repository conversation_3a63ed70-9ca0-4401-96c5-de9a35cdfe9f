<template>
  <div class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="max-w-7xl mx-auto">
      <div class="flex items-center justify-between">
        <!-- Left: Title and Time -->
        <div class="flex items-center gap-6">
          <div>
            <h1 class="text-xl font-semibold text-gray-900">Chấm <PERSON>ng</h1>
            <div class="flex items-center gap-2 text-sm text-green-600 font-medium">
              <div class="w-2 h-2 bg-green-500 rounded-full"></div>
              {{ currentTime }}
            </div>
          </div>
        </div>
        
        <!-- Right: Today's Summary -->
        <div class="text-right">
          <h2 class="text-lg font-semibold text-gray-900">Chấm Công Hôm Nay</h2>
          <p class="text-sm text-gray-600">{{ currentDate }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import { format } from "date-fns";
import { vi } from "date-fns/locale";

// Reactive state
const currentTimeRef = ref(new Date());
let timeInterval: NodeJS.Timeout;

// Computed properties
const currentTime = computed(() => {
  return format(currentTimeRef.value, "HH:mm:ss", { locale: vi });
});

const currentDate = computed(() => {
  return format(currentTimeRef.value, "dd/MM/yyyy", { locale: vi });
});

// Functions
const updateTime = () => {
  currentTimeRef.value = new Date();
};

// Lifecycle
onMounted(() => {
  updateTime();
  timeInterval = setInterval(updateTime, 1000);
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});
</script>
